import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gap/gap.dart';
import 'package:gotcha_mfg_app/config/router/app_router.gr.dart';
import 'package:gotcha_mfg_app/config/theme/app_colors.dart';
import 'package:gotcha_mfg_app/core/extensions/app_text_theme_extension.dart';
import 'package:gotcha_mfg_app/core/storage/storage_service.dart';
import 'package:gotcha_mfg_app/shared/widgets/app_header.dart';
import 'package:gotcha_mfg_app/shared/widgets/primary_button.dart';
import 'package:gotcha_mfg_app/core/utils/platform_utils.dart';
import 'package:gotcha_mfg_app/core/mixpanel_service.dart';
import 'package:gotcha_mfg_app/locator.dart';

@RoutePage()
class OnboardingGoalPage extends StatefulWidget {
  const OnboardingGoalPage({super.key});

  @override
  State<OnboardingGoalPage> createState() => _OnboardingGoalPageState();
}

class _OnboardingGoalPageState extends State<OnboardingGoalPage> {
  String? selectedGoal;
  String? customGoal;
  bool isOtherVisible = false;
  final TextEditingController _customGoalController = TextEditingController();
  
  final List<String> goalOptions = [
    'Stay mentally stable day-to-day',
    'Handle stress & difficult emotions',
    'Understand myself better',
    'Feel less alone',
    'Build confidence',
    'Build consistent mental fitness habits',
    'Improve sleep quality',
    'Manage anxiety better',
    'Develop emotional resilience',
    'Enhance focus and concentration',
    'Build healthy relationships',
    'Overcome negative thoughts',
    'Increase self-awareness',
    'Develop coping strategies',
    'Boost motivation and energy',
    'Practice mindfulness daily'
  ];

  @override
  void initState() {
    super.initState();
    sl<MixpanelService>().trackScreenView(
      'Onboarding Goal Page',
      properties: {'Code': 'screen_view.onboarding_goal_page'},
    );
  }

  @override
  void dispose() {
    _customGoalController.dispose();
    super.dispose();
  }

  void _selectGoal(String goal) {
    setState(() {
      selectedGoal = goal;
      customGoal = null; // Clear custom goal when selecting predefined goal
      isOtherVisible = false; // Hide custom input
    });
  }

  void _selectCustomGoal() {
    setState(() {
      selectedGoal = null; // Clear predefined goal selection
      isOtherVisible = true;
    });
  }

  void _saveCustomGoal(String input) {
    setState(() {
      if (input.trim().isEmpty) {
        customGoal = null;
        selectedGoal = null;
      } else {
        customGoal = input.trim();
        selectedGoal = customGoal; // Set selectedGoal to custom input for consistency
      }
      isOtherVisible = false;
    });
    _customGoalController.clear();
  }

  Future<void> _saveGoalAndContinue() async {
    if (selectedGoal != null) {
      // Save goal to local storage
      await sl<StorageService>().writeData('user_goal', selectedGoal!);

      sl<MixpanelService>().trackButtonClick('Next', properties: {
        'Page': 'Onboarding Goal Page',
        'Selected_Goal': selectedGoal,
        'Code': 'click.onboarding_goal_page.next'
      });

      if (mounted) {
        context.replaceRoute(const OnboardingFeelingRoute());
      }
    }
  }

  Widget _buildGoalChip(String goal) {
    final isSelected = selectedGoal == goal && customGoal == null;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 1),
      child: ActionChip(
        shape: StadiumBorder(
          side: BorderSide(
            width: 1.5,
            color: isSelected ? AppColors.coral : Colors.white,
          ),
        ),
        onPressed: () => _selectGoal(goal),
        label: Text(
          goal,
          style: Theme.of(context).textTheme.ralewaySemiBold.copyWith(
                fontSize: 14,
                color: AppColors.navy,
              ),
        ),
        backgroundColor: isSelected ? AppColors.lightRed : Colors.white,
      ),
    );
  }

  Widget _buildOtherChip() {
    final otherText = customGoal ?? '+ Other';
    final hasCustomGoal = customGoal != null;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 1),
      child: ActionChip(
        shape: StadiumBorder(
          side: BorderSide(
            width: 1.5,
            color: hasCustomGoal ? AppColors.coral : AppColors.midBlue,
          ),
        ),
        onPressed: _selectCustomGoal,
        label: Text(
          otherText,
          style: hasCustomGoal
              ? Theme.of(context).textTheme.ralewaySemiBold.copyWith(
                    fontSize: 14,
                    color: AppColors.navy,
                  )
              : Theme.of(context).textTheme.ralewayLight.copyWith(
                    fontSize: 14,
                    color: Colors.grey,
                  ),
        ),
        backgroundColor: hasCustomGoal ? AppColors.lightRed : Colors.white,
      ),
    );
  }

  Widget _buildCustomInput() {
    if (!isOtherVisible) return const SizedBox.shrink();

    const border = OutlineInputBorder(
      borderRadius: BorderRadius.all(Radius.circular(30)),
      borderSide: BorderSide(color: AppColors.coral, width: 1.5),
    );

    return Container(
      margin: const EdgeInsets.only(top: 8),
      height: 44,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(30),
        color: Colors.white,
      ),
      child: TextField(
        controller: _customGoalController,
        style: Theme.of(context).textTheme.ralewayBold.copyWith(
              fontSize: 14,
              color: AppColors.navy,
            ),
        decoration: InputDecoration(
          hintText: 'Type your goal here...',
          isDense: true,
          contentPadding: const EdgeInsets.symmetric(horizontal: 16),
          enabledBorder: border,
          focusedBorder: border,
          suffixIcon: IconButton(
            icon: const Icon(Icons.check, color: AppColors.coral),
            onPressed: () {
              final input = _customGoalController.text.trim();
              _saveCustomGoal(input);
            },
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final size = MediaQuery.sizeOf(context);

    return Scaffold(
      appBar: AppBar(
        toolbarHeight: 0,
        elevation: 0,
        systemOverlayStyle: const SystemUiOverlayStyle(
          statusBarColor: Colors.white,
          systemNavigationBarIconBrightness: Brightness.dark,
          statusBarBrightness: Brightness.light,
          statusBarIconBrightness: Brightness.dark,
          systemNavigationBarColor: AppColors.grey,
        ),
      ),
      resizeToAvoidBottomInset: true,
      body: Padding(
        padding: EdgeInsets.only(
          top: isIos ? 4 : 8,
          left: 8,
          right: 8,
        ),
        child: Column(
          children: [
            const AppHeader(
              title: 'Goal',
              currentStep: 1,
              totalSteps: 4,
            ),
            Expanded(
              child: Container(
                color: AppColors.navy,
                child: Container(
                  padding: EdgeInsets.only(bottom: isIos ? 80 : 56),
                  decoration: const BoxDecoration(
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(30),
                      topRight: Radius.circular(30),
                    ),
                    color: AppColors.grey,
                  ),
                  child: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Gap(12),
                        Padding(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 24,
                            vertical: 16,
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Let\'s set your goal',
                                style: textTheme.ralewayRegular
                                    .copyWith(fontSize: 14),
                              ),
                              const Gap(4),
                              Text(
                                'What\'s your main goal for using this app?',
                                style: textTheme.ralewaySemiBold.copyWith(
                                  fontSize: 17,
                                ),
                              ),
                            ],
                          ),
                        ),
                        const Gap(16),
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 24),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Wrap(
                                spacing: 15,
                                runSpacing: 8,
                                children: [
                                  ...goalOptions.map((goal) => _buildGoalChip(goal)),
                                  _buildOtherChip(),
                                ],
                              ),
                              _buildCustomInput(),
                            ],
                          ),
                        ),
                        const Gap(120),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
      floatingActionButton: selectedGoal != null
          ? Padding(
              padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 24),
              child: SizedBox(
                width: size.width,
                child: PrimaryButton(
                  text: 'Next',
                  isEnabled: true,
                  onPressed: _saveGoalAndContinue,
                ),
              ),
            )
          : const SizedBox(),
    );
  }
}
